/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.fcst.profits.controller;

import java.util.List;

import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.Consumes;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.Context;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import javax.ws.rs.core.MediaType;

import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import org.apache.cxf.jaxrs.ext.multipart.Attachment;
import org.apache.cxf.jaxrs.ext.multipart.Multipart;
import org.springframework.web.bind.annotation.RequestParam;

import com.huawei.it.fcst.profits.common.vo.CommonResult;
import com.huawei.it.fcst.profits.vo.LabelOperateLogVO;
import com.huawei.it.fcst.profits.vo.request.LabelConfigRequest;
import com.huawei.it.fcst.profits.vo.request.LabelInfoRequest;

/**
 * ILabelAuditController
 *
 * <AUTHOR>
 * @date 2022/10/06 10:34
 **/
@Path("/labelAudit")
@Consumes({"application/json"})
@Produces({"application/json"})
@Tag(name = "标签审视", description = "标签审视相关接口，包括配置查询、版本管理、产品线管理等功能")
public interface ILabelAuditController {
    /**
     * [查询标签配置列表]
     *
     * @param labelConfigVO 入参
     * @return PagedResult<FcstLabelConfigVO> 标签配置信息
     * <AUTHOR>
     * @since 2022年10月10日
     */
    @POST
    @Path("/getConfigDataByPage")
    CommonResult getConfigDataByPage(LabelConfigRequest labelConfigVO);

    /**
     * [查询版本列表]
     *
     * @return PagedResult<FcstLabelConfigVO> 标签配置信息
     * <AUTHOR>
     * @since 2022年10月10日
     */
    @GET
    @Path("/getVersionInfo")
    CommonResult getVersionInfo();

    /**
     * [获取产品线列表信息]
     *
     * @param labelConfigRequest req
     * @return List<CommonVO>  产品线信息
     * <AUTHOR>
     * @since 2022年10月10日
     */
    @POST
    @Path("/getProductLine")
    CommonResult getProductLine(LabelConfigRequest labelConfigRequest);

    /**
     * [获取L1名称]
     *
     * @param request 入参
     * @return List<String> L1名称
     * <AUTHOR>
     * @since 2022年10月10日
     */
    @POST
    @Path("/getL1Names")
    CommonResult getL1Names(LabelConfigRequest request);

    /**
     * [获取L2名称]
     *
     * @param request 入参
     * @return List<String> L2名称
     * <AUTHOR>
     * @since 2022年10月10日
     */
    @POST
    @Path("/getL2Names")
    CommonResult getL2Names(LabelConfigRequest request);

    /**
     * [获取L3名称]
     *
     * @param request 入参
     * @return List<String> L3名称
     * <AUTHOR>
     * @since 2022年10月10日
     */
    @POST
    @Path("/getL3Names")
    CommonResult getL3Names(LabelConfigRequest request);

    /**
     * [查询标签审视信息列表]
     *
     * @param labelConfigRequest 入参
     * @return PagedResult<FcstLabelAuditVO>  查询标签审视信息
     * <AUTHOR>
     * @since 2022年10月10日
     */
    @POST
    @Path("/getAuditDataByPage")
    CommonResult getAuditDataByPage(LabelConfigRequest labelConfigRequest);

    /**
     * [保存标签审视信息]
     *
     * @param labelAuditVOList 入参
     * @return void
     * <AUTHOR>
     * @since 2022年10月10日
     */
    @POST
    @Path("/save")
    CommonResult save(List<LabelInfoRequest> labelAuditVOList);

    /**
     * [提交标签审视信息]
     *
     * @return void
     * <AUTHOR>
     * @since 2022年10月10日
     */
    @GET
    @Path("/submit")
    CommonResult submit();

    /**
     * [服务名称]downImportTemplate 下载导入模板
     *
     * @param response 入参
     * @return CommonResult
     * <AUTHOR>
     */
    @GET
    @Path("/templateDownload")
    CommonResult templateDownload(@Context HttpServletResponse response);

    /**
     * [导入标签审视信息]
     *
     * @param file 入参
     * @param fileName 入参
     * @return void
     * <AUTHOR>
     * @since 2022年10月10日
     */
    @POST
    @Path("/importData")
    @Consumes(MediaType.MULTIPART_FORM_DATA)
    @Produces(MediaType.APPLICATION_JSON + ";charset=UTF-8")
    CommonResult importData(@Multipart("file") Attachment file, @RequestParam("fileName") String fileName) throws CommonApplicationException;

    /**
     * [导出标签审视信息]
     *
     * @param response           response
     * @param labelConfigRequest labelConfigRequest
     * @return void
     * <AUTHOR>
     * @since 2022年10月10日
     */
    @POST
    @Path("/exportData")
    CommonResult exportData(@Context HttpServletResponse response, LabelConfigRequest labelConfigRequest) throws CommonApplicationException;

    /**
     * [标签复盘-获取标签人工修改率]
     *
     * @param year year
     * @param roleId roleId
     * @return List<LabelModifyGraphVO>  人工修改率
     * <AUTHOR>
     * @since 2022年10月10日
     */
    @GET
    @Path("/getManualModifyInfo/{year}/{roleId}")
    CommonResult getManualModifyInfo(@PathParam("year") String year, @PathParam("roleId") String roleId);

    /**
     * 获取查询用户信息
     *
     * @param request 用户对象
     * @return Map
     */
    @POST
    @Path("/getLastUpdatedBys")
    CommonResult getLastUpdatedBys(LabelConfigRequest request);

    /**
     * 获取状态信息
     * @param request 请求参数
     * @return list
     */
    @POST
    @Path("/getStatusList")
    CommonResult getStatusList(LabelConfigRequest request);

    @GET
    @Path("/getLogStatus")
    CommonResult getOperateRecordLogStatus(@QueryParam("") LabelOperateLogVO request);
}
