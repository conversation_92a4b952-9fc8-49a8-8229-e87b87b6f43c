/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.fcst.profits.controller;

import com.huawei.it.fcst.profits.common.vo.CommonResult;
import com.huawei.it.fcst.profits.vo.GroupAnalystsVO;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;

import org.apache.cxf.jaxrs.ext.multipart.Attachment;
import org.apache.cxf.jaxrs.ext.multipart.Multipart;

import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.Consumes;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;

/**
 * IGroupAnalystsController
 **/
@Path("/groupAnalysts")
@Consumes( {"application/json"})
@Produces( {"application/json"})
@Api(tags = "分组分析接口")
@Tag(name = "分组分析", description = "分组分析相关接口，包括查询、导入导出、模板下载等功能")
public interface IGroupAnalystsController {

    /**
     * 查询下拉框功能
     *
     * @param requestVO 任务ID
     * @return CommonResult
     */
    @ApiOperation(value = "查询下拉框功能", notes = "根据任务ID查询下拉框")
    @Operation(summary = "查询下拉框功能", description = "根据任务ID查询下拉框选项")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "查询成功",
                    content = @Content(mediaType = "application/json",
                                     schema = @Schema(implementation = CommonResult.class))),
        @ApiResponse(responseCode = "400", description = "请求参数错误"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @POST
    @Path("/dropDownBox")
    CommonResult dropDownBox(
        @Parameter(description = "分组分析请求参数", required = true, schema = @Schema(implementation = GroupAnalystsVO.class))
        GroupAnalystsVO requestVO) throws CommonApplicationException;

    /**
     * 分页查询功能
     *
     * @param request 任务ID
     * @return CommonResult
     */
    @ApiOperation(value = "分页查询功能", notes = "分页查询分组分析信息")
    @POST
    @Path("/page")
    CommonResult getGroupAnalystsPageInfo(GroupAnalystsVO request);

    /**
     * 导出功能
     *
     * @param request 任务ID
     * @return CommonResult
     */
    @ApiOperation(value = "导出功能", notes = "导出分组分析信息")
    @POST
    @Path("/export")
    CommonResult exportGroupAnalystsInfo(@Context HttpServletResponse response, GroupAnalystsVO request);

    /**
     * 导入功能
     *
     * @param file 任务ID
     * @return CommonResult
     */
    @ApiOperation(value = "导入功能", notes = "导入分组分析信息")
    @POST
    @Path("/import")
    @Consumes(MediaType.MULTIPART_FORM_DATA)
    @Produces(MediaType.APPLICATION_JSON + ";charset=UTF-8")
    CommonResult importGroupAnalystsInfo(@Multipart("file") Attachment file) throws CommonApplicationException;

    /**
     * 全量导入功能
     *
     * @param file 任务ID
     * @return CommonResult
     */
    @ApiOperation(value = "全量导入功能", notes = "全量导入分组分析信息")
    @POST
    @Path("/fullImport")
    @Consumes(MediaType.MULTIPART_FORM_DATA)
    @Produces(MediaType.APPLICATION_JSON + ";charset=UTF-8")
    CommonResult fullImportGroupAnalystsInfo(@Multipart("file") Attachment file) throws CommonApplicationException;

    /**
     * 模板下载
     *
     * @param response 任务ID
     * @return CommonResult
     */
    @ApiOperation(value = "模板下载", notes = "下载分组分析模板")
    @POST
    @Path("/templateDownload")
    CommonResult groupAnalystsTemplateDownload(@Context HttpServletResponse response) throws CommonApplicationException;

    /**
     * 查询任务状态信息
     *
     * @param objectId 对象ID
     * @return CommonResult
     */
    @ApiOperation(value = "查询任务状态信息", notes = "根据对象ID查询任务状态")
    @GET
    @Path("/dataStatus/{objectId}")
    CommonResult queryDataRefreshStatus(@PathParam("objectId") String objectId) throws CommonApplicationException;


    /**
     * 查询任务状态信息
     *
     * @return CommonResult
     */
    @ApiOperation(value = "提交任务", notes = "提交分组分析任务")
    @GET
    @Path("/submit")
    CommonResult submit() throws ApplicationException;

}
