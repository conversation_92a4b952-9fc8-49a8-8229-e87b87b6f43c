package com.huawei.it.fcst.profits.controller;

import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.view.RedirectView;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * API文档访问控制器
 * 提供便捷的API文档访问入口
 * 
 * <AUTHOR>
 * @date 2024-06-17
 */
@RestController
@RequestMapping("/api")
@Tag(name = "API文档", description = "API文档访问相关接口")
public class ApiDocController {

    /**
     * 重定向到Swagger UI主页
     * 访问 /api/doc 即可跳转到Swagger UI界面
     */
    @GetMapping("/doc")
    @Operation(summary = "访问API文档", description = "重定向到Swagger UI界面")
    public RedirectView redirectToSwaggerUi() {
        return new RedirectView("/swagger-ui.html");
    }

    /**
     * 重定向到Swagger UI主页（兼容性路径）
     */
    @GetMapping("/docs")
    @Operation(summary = "访问API文档（兼容路径）", description = "重定向到Swagger UI界面")
    public RedirectView redirectToSwaggerUiCompat() {
        return new RedirectView("/swagger-ui.html");
    }

    /**
     * 获取API文档JSON格式
     */
    @GetMapping("/json")
    @Operation(summary = "获取API文档JSON", description = "获取OpenAPI 3.0格式的JSON文档")
    public RedirectView getApiDocsJson() {
        return new RedirectView("/v3/api-docs");
    }

    /**
     * API文档首页信息
     */
    @GetMapping("/info")
    @Operation(summary = "API文档信息", description = "获取API文档的基本信息和访问链接")
    public ApiDocInfo getApiDocInfo() {
        ApiDocInfo info = new ApiDocInfo();
        info.setTitle("预测利润系统 API 文档");
        info.setVersion("v1.0.0");
        info.setDescription("华为IT预测利润系统的RESTful API接口文档");
        info.setSwaggerUiUrl("/fcst/profits/swagger-ui.html");
        info.setApiDocsUrl("/fcst/profits/v3/api-docs");
        info.setApiDocUrl("/fcst/profits/api/doc");
        return info;
    }

    /**
     * 健康检查接口（隐藏在文档中）
     */
    @GetMapping("/health")
    @Hidden
    public String health() {
        return "API Documentation Service is running";
    }

    /**
     * API文档信息实体类
     */
    public static class ApiDocInfo {
        private String title;
        private String version;
        private String description;
        private String swaggerUiUrl;
        private String apiDocsUrl;
        private String apiDocUrl;

        // Getters and Setters
        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getVersion() {
            return version;
        }

        public void setVersion(String version) {
            this.version = version;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }

        public String getSwaggerUiUrl() {
            return swaggerUiUrl;
        }

        public void setSwaggerUiUrl(String swaggerUiUrl) {
            this.swaggerUiUrl = swaggerUiUrl;
        }

        public String getApiDocsUrl() {
            return apiDocsUrl;
        }

        public void setApiDocsUrl(String apiDocsUrl) {
            this.apiDocsUrl = apiDocsUrl;
        }

        public String getApiDocUrl() {
            return apiDocUrl;
        }

        public void setApiDocUrl(String apiDocUrl) {
            this.apiDocUrl = apiDocUrl;
        }
    }
}
