/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.fcst.profits.controller;

import com.huawei.it.fcst.profits.common.vo.CommonResult;
import com.huawei.it.fcst.profits.vo.request.LabelConfigQueryRequest;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import org.apache.cxf.jaxrs.ext.multipart.Attachment;
import org.apache.cxf.jaxrs.ext.multipart.Multipart;
import org.springframework.web.bind.annotation.RequestParam;

import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.Consumes;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;

/**
 * ILabelConfigController
 *
 * <AUTHOR>
 * @date 2022/10/06 10:34
 **/
@Path("/configManagement")
@Consumes( {"application/json"})
@Produces( {"application/json"})
@Tag(name = "标签配置", description = "标签配置管理相关接口，包括导入导出、查询、提交等功能")
public interface ILabelConfigController {

    /**
     * [服务名称]downImportTemplate 下载导入模板
     *
     * @param response 入参
     * @return CommonResult
     * <AUTHOR>
     */
    @GET
    @Path("/templateConfigDataDownload/{dataType}")
    CommonResult templateConfigDataDownload(@Context HttpServletResponse response,
        @PathParam("dataType") String dataType);

    /**
     * [导入标签配置信息]
     *
     * @param file 入参
     * @return void
     * @throws CommonApplicationException IOException
     * @since 2022年10月10日
     */
    @POST
    @Path("/importConfigCoaData")
    @Consumes(MediaType.MULTIPART_FORM_DATA)
    @Produces(MediaType.APPLICATION_JSON + ";charset=UTF-8")
    CommonResult importConfigCoaData(@Multipart("file") Attachment file, @RequestParam("roleId") String roleId)
        throws CommonApplicationException;

    /**
     * [导入标签配置信息]
     *
     * @param file 入参
     * @return void
     * @throws CommonApplicationException IOException
     * @since 2022年10月10日
     */
    @POST
    @Path("/importConfigPlanComData")
    @Consumes(MediaType.MULTIPART_FORM_DATA)
    @Produces(MediaType.APPLICATION_JSON + ";charset=UTF-8")
    CommonResult importConfigPlanComData(@Multipart("file") Attachment file, @RequestParam("roleId") String roleId)
        throws CommonApplicationException;

    /**
     * [导入标签配置信息]
     *
     * @param file 入参
     * @return void
     * @throws CommonApplicationException IOException
     * @since 2022年10月10日
     */
    @POST
    @Path("/importConfigIctData")
    @Consumes(MediaType.MULTIPART_FORM_DATA)
    @Produces(MediaType.APPLICATION_JSON + ";charset=UTF-8")
    CommonResult importConfigIctData(@Multipart("file") Attachment file, @RequestParam("roleId") String roleId)
        throws CommonApplicationException;

    /**
     * [导入标签配置信息]
     *
     * @param file 入参
     * @return void
     * @throws CommonApplicationException IOException
     * @since 2022年10月10日
     */
    @POST
    @Path("/importConfigObjectData")
    @Consumes(MediaType.MULTIPART_FORM_DATA)
    @Produces(MediaType.APPLICATION_JSON + ";charset=UTF-8")
    CommonResult importConfigObjectData(@Multipart("file") Attachment file, @RequestParam("roleId") String roleId);

    /**
     * [导出标签配置信息]
     *
     * @param response response
     * @param labelConfigRequest labelConfigRequest
     * @return void
     * @since 2022年10月10日
     */
    @POST
    @Path("/exportConfigCoaData")
    CommonResult exportConfigCoaData(@Context HttpServletResponse response, LabelConfigQueryRequest labelConfigRequest);

    /**
     * [导出标签配置信息]
     *
     * @param response response
     * @param labelConfigRequest labelConfigRequest
     * @return void
     * @since 2022年10月10日
     */
    @POST
    @Path("/exportConfigObjectData")
    CommonResult exportConfigObjectData(@Context HttpServletResponse response,
        LabelConfigQueryRequest labelConfigRequest);

    /**
     * [导出标签配置信息]
     *
     * @param response response
     * @param labelConfigRequest labelConfigRequest
     * @return void
     * @since 2022年10月10日
     */
    @POST
    @Path("/exportConfigPlanComData")
    CommonResult exportConfigPlanComData(@Context HttpServletResponse response,
        LabelConfigQueryRequest labelConfigRequest);

    /**
     * [导出标签配置信息]
     *
     * @param response response
     * @param labelConfigRequest labelConfigRequest
     * @return void
     * @since 2022年10月10日
     */
    @POST
    @Path("/exportConfigIctData")
    CommonResult exportConfigIctData(@Context HttpServletResponse response, LabelConfigQueryRequest labelConfigRequest);

    @POST
    @Path("/getCoaConfigInfoByPage")
    CommonResult getCoaConfigInfoByPage(LabelConfigQueryRequest requestVO);

    @POST
    @Path("/getPlanComConfigInfoByPage")
    CommonResult getPlanComConfigInfoByPage(LabelConfigQueryRequest requestVO);

    @POST
    @Path("/getIctConfigInfoByPage")
    CommonResult getIctConfigInfoByPage(LabelConfigQueryRequest requestVO);

    @POST
    @Path("/getCoaProductionNames")
    CommonResult getCoaProductionNames(LabelConfigQueryRequest requestVO);

    @POST
    @Path("/getCoaL1Names")
    CommonResult getCoaL1Names(LabelConfigQueryRequest requestVO);

    @POST
    @Path("/getCoaL2Names")
    CommonResult getCoaL2Names(LabelConfigQueryRequest requestVO);

    @POST
    @Path("/getCoaCodes")
    CommonResult getCoaCodes(LabelConfigQueryRequest requestVO);

    @POST
    @Path("/getPlanComLv1Names")
    CommonResult getPlanComLv1Names(LabelConfigQueryRequest requestVO);

    @POST
    @Path("/getPlanComLv2Names")
    CommonResult getPlanComLv2Names(LabelConfigQueryRequest requestVO);

    @POST
    @Path("/getPlanComLv3Names")
    CommonResult getPlanComLv3Names(LabelConfigQueryRequest requestVO);

    @POST
    @Path("/getBusiLv4Names")
    CommonResult getBusiLv4Names(LabelConfigQueryRequest requestVO);

    @POST
    @Path("/getPlanComL1Names")
    CommonResult getPlanComL1Names(LabelConfigQueryRequest requestVO);

    @POST
    @Path("/getPlanComL2Names")
    CommonResult getPlanComL2Names(LabelConfigQueryRequest requestVO);

    @POST
    @Path("/getIctLv1Names")
    CommonResult getIctLv1Names(LabelConfigQueryRequest requestVO);

    @POST
    @Path("/getIctLv2Names")
    CommonResult getIctLv2Names(LabelConfigQueryRequest requestVO);

    @POST
    @Path("/getIctLv3Names")
    CommonResult getIctLv3Names(LabelConfigQueryRequest requestVO);

    @POST
    @Path("/getIctL1Names")
    CommonResult getIctL1Names(LabelConfigQueryRequest requestVO);

    @POST
    @Path("/getIctArticulationFlagNames")
    CommonResult getIctArticulationFlagNames(LabelConfigQueryRequest requestVO);

    @POST
    @Path("/getObjConfigInfoByPage")
    CommonResult getObjConfigInfoByPage(LabelConfigQueryRequest requestVO);

    @POST
    @Path("/getObjectLv1Names")
    CommonResult getObjectLv1Names(LabelConfigQueryRequest requestVO);

    @POST
    @Path("/getObjectL1Names")
    CommonResult getObjectL1Names(LabelConfigQueryRequest requestVO);

    @POST
    @Path("/getObjectL2Names")
    CommonResult getObjectL2Names(LabelConfigQueryRequest requestVO);

    @POST
    @Path("/getObjectL3Names")
    CommonResult getObjectL3Names(LabelConfigQueryRequest requestVO);

    @POST
    @Path("/getIctUpdatedNames")
    CommonResult getIctUpdatedNames(LabelConfigQueryRequest requestVO);

    @POST
    @Path("/getPlanComUpdatedNames")
    CommonResult getPlanComUpdatedNames(LabelConfigQueryRequest requestVO);

    @POST
    @Path("/getCoaUpdatedNames")
    CommonResult getCoaUpdatedNames(LabelConfigQueryRequest requestVO);

    @POST
    @Path("/getObjectUpdatedNames")
    CommonResult getObjectUpdatedNames(LabelConfigQueryRequest requestVO);

    @POST
    @Path("/submitConfigCoaData")
    CommonResult submitConfigCoaData(LabelConfigQueryRequest requestVO);

    @POST
    @Path("/submitConfigPlanComData")
    CommonResult submitConfigPlanComData(LabelConfigQueryRequest requestVO);

    @POST
    @Path("/submitConfigIctData")
    CommonResult submitConfigIctData(LabelConfigQueryRequest requestVO) throws ApplicationException;

    @POST
    @Path("/submitConfigObjectData")
    CommonResult submitConfigObjectData(LabelConfigQueryRequest requestVO);

    @POST
    @Path("/getPlanComProductionNames")
    CommonResult getPlanComProductionNames(LabelConfigQueryRequest requestVO);

    @POST
    @Path("/queryIctProductInfo")
    CommonResult queryIctProductInfoByPage(LabelConfigQueryRequest requestVO);

    @POST
    @Path("/exportIctProductInfo")
    CommonResult exportIctProductInfo(@Context HttpServletResponse response, LabelConfigQueryRequest labelConfigRequest);

    @POST
    @Path("/queryCoaProductInfo")
    CommonResult queryCoaProductInfoByPage(LabelConfigQueryRequest requestVO);

    @POST
    @Path("/exportCoaProductInfo")
    CommonResult exportCoaProductInfo(@Context HttpServletResponse response, LabelConfigQueryRequest labelConfigRequest);

    @POST
    @Path("/queryPlanComProductInfo")
    CommonResult queryPlanComProductInfoByPage(LabelConfigQueryRequest requestVO);
    @POST
    @Path("/exportPlanComProductInfo")
    CommonResult exportPlanComProductInfo(@Context HttpServletResponse response, LabelConfigQueryRequest labelConfigRequest);

    @POST
    @Path("/getIctMonitorCondition")
    CommonResult getIctMonitorCondition(LabelConfigQueryRequest requestVO);

    @POST
    @Path("/getIctMonitorVersion")
    CommonResult getIctMonitorVersion(LabelConfigQueryRequest requestVO);
    @POST
    @Path("/getIctMonitorLastUpdated")
    CommonResult getIctMonitorLastUpdated(LabelConfigQueryRequest requestVO);

    @POST
    @Path("/getIctConfigCondition")
    CommonResult getIctConfigCondition(LabelConfigQueryRequest requestVO);
    @POST
    @Path("/getPlanComMonitorCondition")
    CommonResult getPlanComMonitorCondition(LabelConfigQueryRequest requestVO);
    @POST
    @Path("/getPlanComMonitorVersion")
    CommonResult getPlanComMonitorVersion(LabelConfigQueryRequest requestVO);
    @POST
    @Path("/getPlanComMonitorLastUpdated")
    CommonResult getPlanComMonitorLastUpdated(LabelConfigQueryRequest requestVO);
    @POST
    @Path("/getCoaMonitorCondition")
    CommonResult getCoaMonitorCondition(LabelConfigQueryRequest requestVO);
    @POST
    @Path("/getCoaMonitorVersion")
    CommonResult getCoaMonitorVersion(LabelConfigQueryRequest requestVO);
    @POST
    @Path("/getCoaMonitorProductName")
    CommonResult getCoaMonitorProductName(LabelConfigQueryRequest requestVO);
    @POST
    @Path("/getCoaMonitorLastUpdated")
    CommonResult getCoaMonitorLastUpdated(LabelConfigQueryRequest requestVO);

    @POST
    @Path("/getObjectAuditCondition")
    CommonResult getObjectAuditCondition(LabelConfigQueryRequest requestVO);
    @POST
    @Path("/getObjectConfigCondition")
    CommonResult getObjectConfigCondition(LabelConfigQueryRequest requestVO);
    @POST
    @Path("/getPlanComConfigCondition")
    CommonResult getPlanComConfigCondition(LabelConfigQueryRequest requestVO);
    @POST
    @Path("/getCoaConfigCondition")
    CommonResult getCoaConfigCondition(LabelConfigQueryRequest requestVO);

    @POST
    @Path("/getPlanComSopInfo")
    CommonResult getPlanComSopInfo(LabelConfigQueryRequest requestVO);

    @POST
    @Path("/importObjConfigAllData")
    @Consumes(MediaType.MULTIPART_FORM_DATA)
    @Produces(MediaType.APPLICATION_JSON + ";charset=UTF-8")
    CommonResult importConfigObjectAllData(@Multipart("file") Attachment file, @RequestParam("roleId") String roleId);

    @POST
    @Path("/importCoaConfigAllData")
    @Consumes(MediaType.MULTIPART_FORM_DATA)
    @Produces(MediaType.APPLICATION_JSON + ";charset=UTF-8")
    CommonResult importCoaConfigAllData(@Multipart("file") Attachment file, @RequestParam("roleId") String roleId);

    @POST
    @Path("/importPlanComConfigAllData")
    @Consumes(MediaType.MULTIPART_FORM_DATA)
    @Produces(MediaType.APPLICATION_JSON + ";charset=UTF-8")
    CommonResult importPlanComConfigAllData(@Multipart("file") Attachment file, @RequestParam("roleId") String roleId);

    @POST
    @Path("/importIctConfigAllData")
    @Consumes(MediaType.MULTIPART_FORM_DATA)
    @Produces(MediaType.APPLICATION_JSON + ";charset=UTF-8")
    CommonResult importIctConfigAllData(@Multipart("file") Attachment file, @RequestParam("roleId") String roleId);

    @GET
    @Path("/initIctConfigCache")
    void initIctConfigCache();

}
